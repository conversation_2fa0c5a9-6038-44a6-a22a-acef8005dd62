/*
 * SPDX-License-Identifier: AGPL-3.0-only
 * unsaved-changes-dialog.tsx
 * Copyright (C) 2025 Nextify Limited
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as
 * published by the Free Software Foundation, either version 3 of the
 * License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 *
 */

'use client'

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@libra/ui/components/alert-dialog'
import * as m from '@/paraglide/messages'

interface UnsavedChangesDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onContinue: () => void
  onDiscard: () => void
}

/**
 * Unsaved changes confirmation dialog
 */
export function UnsavedChangesDialog({
  open,
  onOpenChange,
  onContinue,
  onDiscard,
}: UnsavedChangesDialogProps) {
  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent className='max-w-md'>
        <AlertDialogHeader>
          <AlertDialogTitle>{m["dashboard.projectDetailsTabs.danger.unsavedChanges.title"]()}</AlertDialogTitle>
          <AlertDialogDescription>
            {m["dashboard.projectDetailsTabs.danger.unsavedChanges.description"]()}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel onClick={onContinue}>{m["dashboard.projectDetailsTabs.danger.unsavedChanges.continueEditing"]()}</AlertDialogCancel>
          <AlertDialogAction onClick={onDiscard}>{m["dashboard.projectDetailsTabs.danger.unsavedChanges.discardAndLeave"]()}</AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}