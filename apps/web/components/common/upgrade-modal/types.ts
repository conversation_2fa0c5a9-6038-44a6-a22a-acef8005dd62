/*
 * SPDX-License-Identifier: AGPL-3.0-only
 * types.ts
 * Copyright (C) 2025 Nextify Limited
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as
 * published by the Free Software Foundation, either version 3 of the
 * License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 *
 */

export type UpgradeScenario = 'edit-mode' | 'ai-model' | 'project-limit' | 'private-project' | 'custom'

export type UpgradeModalVariant = 'minimal' | 'full'

export interface UpgradeFeature {
  id: string
  text: string
  highlight?: boolean
}

export interface UpgradeModalConfig {
  scenario: UpgradeScenario
  title?: string
  description?: string
  features?: UpgradeFeature[]
  targetPlan?: 'PRO' | 'MAX'
  buttonText?: string
  variant?: UpgradeModalVariant
  onUpgrade?: () => void
  onCancel?: () => void
}

export interface UpgradeModalContextValue {
  isOpen: boolean
  config: UpgradeModalConfig | null
  showUpgradeModal: (config: UpgradeModalConfig) => void
  hideUpgradeModal: () => void
} 